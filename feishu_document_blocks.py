#!/usr/bin/env python3
"""
飞书文档块获取工具
使用飞书开放API获取文档的所有块信息
"""

import requests
import json
import os
from typing import Dict, List, Optional

class FeishuDocumentAPI:
    def __init__(self, app_id: str, app_secret: str):
        """
        初始化飞书API客户端
        
        Args:
            app_id: 飞书应用ID
            app_secret: 飞书应用密钥
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.base_url = "https://open.feishu.cn/open-apis"
        self.access_token = None
    
    def get_tenant_access_token(self) -> str:
        """获取tenant_access_token"""
        url = f"{self.base_url}/auth/v3/tenant_access_token/internal"
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        result = response.json()
        if result.get("code") != 0:
            raise Exception(f"获取access_token失败: {result}")
        
        self.access_token = result["tenant_access_token"]
        return self.access_token
    
    def get_document_blocks(self, document_id: str, page_size: int = 500, page_token: str = None) -> Dict:
        """
        获取文档的所有块
        
        Args:
            document_id: 文档ID
            page_size: 分页大小，最大500
            page_token: 分页标记
            
        Returns:
            包含文档块信息的字典
        """
        if not self.access_token:
            self.get_tenant_access_token()
        
        url = f"{self.base_url}/docx/v1/documents/{document_id}/blocks"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        params = {
            "page_size": page_size
        }
        if page_token:
            params["page_token"] = page_token
        
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        result = response.json()
        if result.get("code") != 0:
            raise Exception(f"获取文档块失败: {result}")
        
        return result
    
    def get_all_document_blocks(self, document_id: str) -> List[Dict]:
        """
        获取文档的所有块（处理分页）
        
        Args:
            document_id: 文档ID
            
        Returns:
            所有文档块的列表
        """
        all_blocks = []
        page_token = None
        
        while True:
            result = self.get_document_blocks(document_id, page_token=page_token)
            
            # 添加当前页的块
            blocks = result.get("data", {}).get("items", [])
            all_blocks.extend(blocks)
            
            # 检查是否还有下一页
            page_token = result.get("data", {}).get("page_token")
            has_more = result.get("data", {}).get("has_more", False)
            
            if not has_more or not page_token:
                break
        
        return all_blocks
    
    def print_blocks_info(self, blocks: List[Dict]):
        """打印块信息的摘要"""
        print(f"文档总共包含 {len(blocks)} 个块")
        print("\n块类型统计:")
        
        block_types = {}
        for block in blocks:
            block_type = block.get("block_type", "unknown")
            block_types[block_type] = block_types.get(block_type, 0) + 1
        
        for block_type, count in block_types.items():
            print(f"  {block_type}: {count}")
        
        print("\n前10个块的详细信息:")
        for i, block in enumerate(blocks[:10]):
            print(f"\n块 {i+1}:")
            print(f"  ID: {block.get('block_id', 'N/A')}")
            print(f"  类型: {block.get('block_type', 'N/A')}")
            print(f"  父块ID: {block.get('parent_id', 'N/A')}")
            
            # 根据块类型显示相关内容
            if block.get("block_type") == "text":
                text_content = block.get("text", {})
                if text_content:
                    print(f"  文本内容: {str(text_content)[:100]}...")
            elif block.get("block_type") == "heading1":
                heading_content = block.get("heading1", {})
                if heading_content:
                    print(f"  标题内容: {str(heading_content)[:100]}...")


def main():
    """主函数"""
    # 从环境变量获取配置
    app_id = os.getenv("FEISHU_APP_ID")
    app_secret = os.getenv("FEISHU_APP_SECRET")
    
    if not app_id or not app_secret:
        print("请设置环境变量:")
        print("export FEISHU_APP_ID='your_app_id'")
        print("export FEISHU_APP_SECRET='your_app_secret'")
        return
    
    # 文档ID（从您提供的链接中提取的实际文档token）
    document_id = "B4LudmzddoWV7txI1rMc46VunMd"
    
    try:
        # 创建API客户端
        api = FeishuDocumentAPI(app_id, app_secret)
        
        # 获取所有文档块
        print(f"正在获取文档 {document_id} 的所有块...")
        blocks = api.get_all_document_blocks(document_id)
        
        # 打印块信息
        api.print_blocks_info(blocks)
        
        # 保存完整的块数据到文件
        output_file = f"document_blocks_{document_id}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(blocks, f, ensure_ascii=False, indent=2)
        
        print(f"\n完整的块数据已保存到: {output_file}")
        
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
