# 飞书文档块获取工具

这个工具可以帮助您使用飞书开放API获取文档的所有块信息。

## 文档信息

- **文档链接**: https://kttfkmbfmy.feishu.cn/wiki/QmPXwm8XjipW7SkYFBuc8TtTngd
- **Wiki节点Token**: QmPXwm8XjipW7SkYFBuc8TtTngd
- **实际文档Token**: B4LudmzddoWV7txI1rMc46VunMd
- **文档类型**: docx (新版文档)
- **文档标题**: 前端执行方案模版 - 20250714试行

## 已获取的文档内容概览

通过飞书MCP工具，我已经成功获取到了文档的纯文本内容，包含：

1. **天枢任务**: https://yx.mail.netease.com/dubhe#/issues/detail?id=1078815&noReturn=true

2. **功能点1**: 线下门店退退货类型查看详情异常
   - 类型: 修改
   - 服务: yanxuan-kefu-admin
   - 文件路径: src/main/webapp/scripts/controllers/channel/offlineStoreAfterSaleCtrl.js
   - 问题: 调用的后端接口缺失 expectTime 字段

3. **功能点2**: 游戏序列化管理平台新增激活序列号列表
   - 类型: 新建
   - 项目: https://git.yx.netease.com/yanxuan/dhxy-workbench
   - 页面路径: #/serialnumber/list
   - 包含完整的接口定义和数据结构

## 使用Python脚本获取结构化块信息

如果您需要获取更详细的文档块结构信息，可以使用提供的Python脚本：

### 1. 安装依赖

```bash
pip install requests
```

### 2. 设置环境变量

```bash
export FEISHU_APP_ID='your_app_id'
export FEISHU_APP_SECRET='your_app_secret'
```

### 3. 运行脚本

```bash
python feishu_document_blocks.py
```

### 4. 脚本功能

- 自动获取tenant_access_token
- 调用 `/open-apis/docx/v1/documents/{document_id}/blocks` API
- 处理分页，获取所有文档块
- 统计块类型分布
- 显示前10个块的详细信息
- 将完整数据保存为JSON文件

## API接口说明

### 获取文档块

**接口**: `GET /open-apis/docx/v1/documents/{document_id}/blocks`

**参数**:
- `document_id`: 文档ID (B4LudmzddoWV7txI1rMc46VunMd)
- `page_size`: 分页大小，最大500
- `page_token`: 分页标记

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "items": [
      {
        "block_id": "doxcn...",
        "block_type": "text",
        "parent_id": "doxcn...",
        "children": ["doxcn..."],
        "text": {
          "elements": [
            {
              "text_run": {
                "content": "文本内容"
              }
            }
          ]
        }
      }
    ],
    "page_token": "next_page_token",
    "has_more": true
  }
}
```

## 常见块类型

- `text`: 普通文本
- `heading1`: 一级标题
- `heading2`: 二级标题
- `heading3`: 三级标题
- `bullet`: 无序列表
- `ordered`: 有序列表
- `code`: 代码块
- `quote`: 引用
- `equation`: 公式
- `todo`: 待办事项
- `bitable`: 多维表格
- `callout`: 高亮块
- `chat_card`: 群名片
- `diagram`: 流程图
- `divider`: 分割线
- `file`: 文件
- `grid`: 分栏
- `grid_column`: 分栏列
- `iframe`: 内嵌网页
- `image`: 图片
- `isv`: 第三方应用
- `mindnote`: 思维笔记
- `sheet`: 电子表格
- `table`: 表格
- `table_cell`: 表格单元格
- `view`: 视图

## 注意事项

1. 需要有效的飞书应用凭证
2. 应用需要有文档读取权限
3. 文档需要对应用可见
4. API有调用频率限制
5. 大文档可能需要多次分页请求

## 错误处理

脚本包含完整的错误处理机制：
- 网络请求异常
- API响应错误
- 权限不足
- 文档不存在等情况
